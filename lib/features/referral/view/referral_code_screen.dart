import 'package:auto_route/auto_route.dart';
import 'package:banachef/core/bloc/bloc_exports.dart';
import 'package:flutter/services.dart';
import '../../../core/di/injection_container.dart';
import '../../../ui/kit/ui_kit.dart';
import '../cubit/referral_cubit.dart';
import '../cubit/referral_state.dart';
import '../widgets/referral_header.dart';
import '../widgets/referral_benefits.dart';
import '../widgets/referral_input_section.dart';

@RoutePage(name: 'ReferralCodeRoute')
class ReferralCodeScreen extends BaseCubitView<ReferralCubit> {
  const ReferralCodeScreen({super.key});

  @override
  ReferralCubit createCubit(BuildContext context) {
    return getIt<ReferralCubit>();
  }

  @override
  Widget buildContent(BuildContext context, dynamic state) {
    return const _ReferralCodeContent();
  }


}

class _ReferralCodeContent extends StatefulWidget {
  const _ReferralCodeContent();

  @override
  State<_ReferralCodeContent> createState() => _ReferralCodeContentState();
}

class _ReferralCodeContentState extends State<_ReferralCodeContent> {
  final TextEditingController _codeController = TextEditingController();

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: BanaColors.background,
      body: SafeArea(
        child: BlocListener<ReferralCubit, ReferralState>(
          listener: (context, state) {
            if (state is ReferralSuccess) {
              // Haptic feedback for success
              HapticFeedback.lightImpact();

              // Show success toast
              BanaToast.success(
                context: context,
                message: state.message ?? 'Áp dụng mã giới thiệu thành công!',
                duration: const Duration(seconds: 3),
              );

              // Navigate to next screen after short delay
              Future.delayed(const Duration(milliseconds: 1500), () {
                if (context.mounted) {
                  _navigateToNextScreen(context);
                }
              });
            } else if (state is ReferralError) {
              // Haptic feedback for error
              HapticFeedback.heavyImpact();

              // Show error toast
              BanaToast.error(
                context: context,
                message: state.message,
                duration: const Duration(seconds: 4),
                showCloseButton: true,
              );
            } else if (state is ReferralAlreadyHasCode) {
              // Show info toast for existing referral code
              BanaToast.info(
                context: context,
                message: state.message,
                duration: const Duration(seconds: 3),
              );
            }
          },
          child: BlocBuilder<ReferralCubit, ReferralState>(
            builder: (context, state) {
              return ResponsiveContainer(
                padding: BanaSpacing.all.lg,
                child: Column(
                  children: [
                    // Header với mascot
                    const ReferralHeader(),

                    BanaSpacing.verticalSpacing.xl,

                    // Giải thích lợi ích
                    const ReferralBenefits(),

                    BanaSpacing.verticalSpacing.xl,

                    // Khu vực nhập liệu
                    ReferralInputSection(
                      controller: _codeController,
                      state: state,
                      onApply: () => _applyReferralCode(context),
                      onChanged: (value) => _onTextChanged(context),
                    ),

                    const Spacer(),

                    // Skip button
                    _buildSkipButton(context),

                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  void _applyReferralCode(BuildContext context) {
    final code = _codeController.text.trim();

    // Validation
    if (code.isEmpty) {
      BanaToast.warning(
        context: context,
        message: 'Vui lòng nhập mã giới thiệu',
        duration: const Duration(seconds: 2),
      );
      return;
    }

    if (code.length < 3) {
      BanaToast.warning(
        context: context,
        message: 'Mã giới thiệu phải có ít nhất 3 ký tự',
        duration: const Duration(seconds: 2),
      );
      return;
    }

    // Apply referral code
    final cubit = context.read<ReferralCubit>();
    cubit.applyReferralCode(code);
  }

  void _onTextChanged(BuildContext context) {
    final cubit = context.read<ReferralCubit>();
    cubit.clearError();
  }

  Widget _buildSkipButton(BuildContext context) {
    return BanaButton.text(
      text: 'Bỏ qua, tôi không có mã',
      onPressed: () => _navigateToNextScreen(context),
    );
  }

  void _navigateToNextScreen(BuildContext context) {
    // TODO: Navigate to dashboard/home screen
    context.router.pushAndClearStack('/dashboard');
  }
}
