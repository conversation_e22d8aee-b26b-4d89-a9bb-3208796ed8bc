import '../../../core/bloc/bloc_exports.dart';
import '../../../core/di/injection_container.dart';
import '../models/onboarding_data.dart';
import 'onboarding_summary_state.dart';

/// Cubit for managing onboarding summary screen
class OnboardingSummaryCubit extends BaseCubit<OnboardingSummaryState> {
  OnboardingSummaryCubit() : super(const OnboardingSummaryInitial());

  /// Generate summary data with AI meal plan
  Future<void> generateSummary(OnboardingData onboardingData, String userName) async {
    await executeWithErrorHandling(
      () async {
        emit(const OnboardingSummaryLoading(
          loadingMessage: 'Chef <PERSON><PERSON> đang tạo kế hoạch bữa ăn dành riêng cho bạn...',
        ));

        // Simulate AI meal plan generation
        await Future.delayed(const Duration(seconds: 2));

        final mealPlan = _generateMealPlan(onboardingData);
        
        final summaryData = OnboardingSummaryData(
          onboardingData: onboardingData,
          mealPlan: mealPlan,
          userName: userName,
        );

        emit(OnboardingSummarySuccess(
          summaryData,
          message: '<PERSON>ế hoạch bữa ăn đã được tạo thành công!',
        ));
      },
    );
  }

  /// Generate meal plan based on onboarding data
  List<MealPlanItem> _generateMealPlan(OnboardingData data) {
    // Generate breakfast
    final breakfast = _generateBreakfast(data);
    
    // Generate lunch
    final lunch = _generateLunch(data);
    
    // Generate dinner
    final dinner = _generateDinner(data);

    return [breakfast, lunch, dinner];
  }

  /// Generate breakfast recommendation
  MealPlanItem _generateBreakfast(OnboardingData data) {
    // Logic based on user preferences
    String dishName = 'Trứng cuộn rau củ';
    String chefNote = 'Giàu protein để hỗ trợ mục tiêu tăng cơ';
    
    // Customize based on allergies
    if (data.allergies.contains('Trứng')) {
      dishName = 'Cháo yến mạch trái cây';
      chefNote = 'Không chứa trứng (theo yêu cầu dị ứng của bạn) và giàu chất xơ';
    }
    
    // Customize based on dietary lifestyle
    if (data.dietaryLifestyles.contains('Chay')) {
      dishName = 'Smoothie bowl trái cây';
      chefNote = 'Hoàn toàn thuần chay và cung cấp năng lượng tự nhiên';
    }

    return MealPlanItem(
      mealType: 'Bữa Sáng',
      dishName: dishName,
      chefNote: chefNote,
    );
  }

  /// Generate lunch recommendation
  MealPlanItem _generateLunch(OnboardingData data) {
    String dishName = 'Salad Gà Kiểu Thái';
    String chefNote = 'Phù hợp với sở thích ẩm thực Thái Lan và là một lựa chọn tuyệt vời cho mục tiêu giảm cân';
    
    // Customize based on cuisine preferences
    if (data.cuisinePreferences.contains('Việt Nam')) {
      dishName = 'Bún chả Hà Nội';
      chefNote = 'Món ăn truyền thống Việt Nam yêu thích của bạn, được điều chỉnh để phù hợp với mục tiêu sức khỏe';
    } else if (data.cuisinePreferences.contains('Ý')) {
      dishName = 'Pasta Primavera';
      chefNote = 'Ẩm thực Ý với nhiều rau củ tươi, phù hợp với sở thích của bạn';
    }
    
    // Customize based on allergies
    if (data.allergies.contains('Hải sản')) {
      chefNote += ' và không chứa hải sản (theo yêu cầu dị ứng của bạn)';
    }

    return MealPlanItem(
      mealType: 'Bữa Trưa',
      dishName: dishName,
      chefNote: chefNote,
    );
  }

  /// Generate dinner recommendation
  MealPlanItem _generateDinner(OnboardingData data) {
    String dishName = 'Cá hồi áp chảo măng tây';
    String chefNote = 'Một công thức ${data.cookingSkill?.displayName ?? "Nâng cao"} để thử thách kỹ năng của bạn';
    
    // Customize based on cooking time
    if (data.cookingTime != null) {
      final timeText = data.cookingTime!.displayName;
      chefNote += ', chỉ mất $timeText để chuẩn bị';
    }
    
    // Customize based on allergies
    if (data.allergies.contains('Hải sản')) {
      dishName = 'Thịt bò xào rau củ';
      chefNote = 'Không chứa hải sản (theo yêu cầu dị ứng của bạn) và giàu protein';
    }
    
    // Customize based on dietary lifestyle
    if (data.dietaryLifestyles.contains('Chay')) {
      dishName = 'Đậu hũ sốt nấm';
      chefNote = 'Hoàn toàn thuần chay và đầy đủ dinh dưỡng';
    }

    return MealPlanItem(
      mealType: 'Bữa Tối',
      dishName: dishName,
      chefNote: chefNote,
    );
  }

  @override
  void reset() {
    emit(const OnboardingSummaryInitial());
  }
}
