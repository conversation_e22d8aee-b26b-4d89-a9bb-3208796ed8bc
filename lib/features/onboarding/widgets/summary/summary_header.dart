import 'package:flutter/material.dart';
import '../../../../ui/kit/ui_kit.dart';

/// Header widget for onboarding summary screen
/// Shows Chef <PERSON><PERSON> mascot with welcome message
class SummaryHeader extends StatelessWidget {
  final String userName;

  const SummaryHeader({
    super.key,
    required this.userName,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Chef <PERSON><PERSON> Ma<PERSON>t
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: BanaColors.primary.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: const Center(
            child: Text(
              '👨‍🍳',
              style: TextStyle(fontSize: 60),
            ),
          ),
        ),

        BanaSpacing.verticalSpacing.lg,

        // Welcome title
        Text(
          'Tuyệt vời, $userName!',
          style: BanaTypography.title1.copyWith(
            color: BanaColors.text,
          ),
          textAlign: TextAlign.center,
        ),

        BanaSpacing.verticalSpacing.md,

        // Subtitle
        Text(
          'Chef <PERSON><PERSON> đã thiết lập xong hồ sơ ẩm thực dành riêng cho bạn. Hãy xem qua nhé!',
          style: BanaTypography.bodyLarge.copyWith(
            color: BanaColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
