import 'package:flutter/material.dart';
import '../../../../ui/kit/ui_kit.dart';
import '../../models/onboarding_data.dart';

/// Widget to display user profile summary
class ProfileSummary extends StatelessWidget {
  final OnboardingData onboardingData;

  const ProfileSummary({
    super.key,
    required this.onboardingData,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          '<PERSON><PERSON> sơ của bạn đã được thiết lập:',
          style: BanaTypography.title3.copyWith(
            color: BanaColors.text,
          ),
        ),

        BanaSpacing.verticalSpacing.lg,

        // Profile items
        ..._buildProfileItems(),

        BanaSpacing.verticalSpacing.lg,

        // Edit note
        BanaButton.text(
          text: 'Bạn luôn có thể thay đổi các thông tin này trong phần <PERSON> nhân.',
          onPressed: () {
            // TODO: Navigate to profile edit
          },
        ),
      ],
    );
  }

  List<Widget> _buildProfileItems() {
    final items = <Widget>[];

    // Health goals
    if (onboardingData.dietaryLifestyles.isNotEmpty) {
      items.add(_buildProfileItem(
        icon: '🎯',
        title: 'Mục tiêu chính',
        content: onboardingData.dietaryLifestyles.join(', '),
      ));
    }

    // Favorite cuisines
    if (onboardingData.cuisinePreferences.isNotEmpty) {
      items.add(_buildProfileItem(
        icon: '❤️',
        title: 'Ẩm thực yêu thích',
        content: onboardingData.cuisinePreferences.join(', '),
      ));
    }

    // Allergies and dislikes
    final avoidItems = <String>[];
    if (onboardingData.allergies.isNotEmpty) {
      avoidItems.addAll(onboardingData.allergies.map((a) => '$a (dị ứng)'));
    }
    if (onboardingData.dislikedFood.isNotEmpty) {
      avoidItems.addAll(onboardingData.dislikedFood);
    }
    if (avoidItems.isNotEmpty) {
      items.add(_buildProfileItem(
        icon: '❌',
        title: 'Cần tránh',
        content: avoidItems.join(', '),
      ));
    }

    // Cooking skill
    if (onboardingData.cookingSkill != null) {
      items.add(_buildProfileItem(
        icon: '🧑‍🍳',
        title: 'Trình độ nấu ăn',
        content: onboardingData.cookingSkill!.displayName,
      ));
    }

    return items;
  }

  Widget _buildProfileItem({
    required String icon,
    required String title,
    required String content,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: BanaSpacing.md),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: BanaColors.primary.withOpacity(0.1),
              borderRadius: BanaBorders.radius.sm,
            ),
            child: Center(
              child: Text(
                icon,
                style: TextStyle(fontSize: 16),
              ),
            ),
          ),

          BanaSpacing.horizontalSpacing.md,

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: BanaTypography.bodyMedium.copyWith(
                    color: BanaColors.textSecondary,
                    fontWeight: BanaTypography.semiBold,
                  ),
                ),
                
                BanaSpacing.verticalSpacing.xs,
                
                Text(
                  content,
                  style: BanaTypography.bodyMedium.copyWith(
                    color: BanaColors.text,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
